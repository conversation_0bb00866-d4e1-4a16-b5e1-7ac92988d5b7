#!/usr/bin/env elixir

# Add the lib directory to the code path
Code.prepend_path("lib")

# Test script to verify that Operations with Ecto extension work during compilation
# when the Drops application hasn't started yet

# Clear the persistent term to simulate app not started
try do
  :persistent_term.erase({:drops_config, :registered_extensions})
rescue
  _ -> :ok
end

IO.puts("Testing Operations compilation without Drops app started...")

# Try to define an operation with Ecto extension
defmodule TestRepo do
  # Mock repo for testing
end

defmodule TestBaseOperations do
  use Drops.Operations, repo: TestRepo
end

defmodule TestEctoOperation do
  use TestBaseOperations, type: :command

  def execute(context) do
    {:ok, context}
  end
end

IO.puts("✓ Operation compiled successfully")

# Check if Ecto extension functions are available
functions = TestEctoOperation.__info__(:functions)
IO.puts("Available functions: #{inspect(functions)}")

ecto_functions = [
  {:changeset, 1},
  {:validate_changeset, 1},
  {:cast_changeset, 1},
  {:ecto_schema, 0}
]

missing_functions = Enum.reject(ecto_functions, fn func -> func in functions end)

if missing_functions == [] do
  IO.puts("✓ All Ecto extension functions are available")
else
  IO.puts("✗ Missing Ecto extension functions: #{inspect(missing_functions)}")
end

IO.puts("Test completed.")
